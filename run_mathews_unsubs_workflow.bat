@echo off
setlocal enabledelayedexpansion

:: Set the working directory to the script location
cd /d "C:\Users\<USER>\OneDrive\My Files"

:: Set console colors and title
title Mathews Unsubscribers Workflow
color 0A

echo ========================================
echo    MATHEWS UNSUBSCRIBERS WORKFLOW
echo ========================================
echo.
echo This batch file will execute the following scripts in order:
echo 1. <PERSON><PERSON> Mathews Unsubs CSV Download
echo 2. Mathews Postpanel Info PP Unsubs CSV Download  
echo 3. MI Conferences Postpanel Info PP Unsubs CSV Download
echo 4. Concatenate CMS Unsubs
echo 5. Concatenate Postpanel Unsubs
echo 6. <PERSON> Unsubs Upload to DBMS
echo.
echo ========================================
echo.

:: Function to check if script execution was successful
set "error_occurred=false"

:: Step 1: <PERSON><PERSON> Mathews Unsubs CSV Download
echo [%time%] Step 1/6: Running <PERSON><PERSON> Mathews Unsubs CSV Download...
echo ----------------------------------------
python "cms_mathews_unsubs_csv_download.py"
if !errorlevel! neq 0 (
    echo ERROR: CMS Mathews Unsubs CSV Download failed with error code !errorlevel!
    set "error_occurred=true"
    goto :error_handler
) else (
    echo SUCCESS: CMS Mathews Unsubs CSV Download completed successfully
)
echo.

:: Step 2: Mathews Postpanel Info PP Unsubs CSV Download
echo [%time%] Step 2/6: Running Mathews Postpanel Info PP Unsubs CSV Download...
echo ----------------------------------------
python "mathews.postpanel.info_pp_unsubs_csv_download.py"
if !errorlevel! neq 0 (
    echo ERROR: Mathews Postpanel Info PP Unsubs CSV Download failed with error code !errorlevel!
    set "error_occurred=true"
    goto :error_handler
) else (
    echo SUCCESS: Mathews Postpanel Info PP Unsubs CSV Download completed successfully
)
echo.

:: Step 3: MI Conferences Postpanel Info PP Unsubs CSV Download
echo [%time%] Step 3/6: Running MI Conferences Postpanel Info PP Unsubs CSV Download...
echo ----------------------------------------
python "miconferences.postpanel.info_pp_unsubs_csv_download.py"
if !errorlevel! neq 0 (
    echo ERROR: MI Conferences Postpanel Info PP Unsubs CSV Download failed with error code !errorlevel!
    set "error_occurred=true"
    goto :error_handler
) else (
    echo SUCCESS: MI Conferences Postpanel Info PP Unsubs CSV Download completed successfully
)
echo.

:: Step 4: Concatenate CMS Unsubs
echo [%time%] Step 4/6: Running Concatenate CMS Unsubs...
echo ----------------------------------------
python "concatenate_cms_unsubs.py"
if !errorlevel! neq 0 (
    echo ERROR: Concatenate CMS Unsubs failed with error code !errorlevel!
    set "error_occurred=true"
    goto :error_handler
) else (
    echo SUCCESS: Concatenate CMS Unsubs completed successfully
)
echo.

:: Step 5: Concatenate Postpanel Unsubs
echo [%time%] Step 5/6: Running Concatenate Postpanel Unsubs...
echo ----------------------------------------
python "concatenate_postpanel_unsubs.py"
if !errorlevel! neq 0 (
    echo ERROR: Concatenate Postpanel Unsubs failed with error code !errorlevel!
    set "error_occurred=true"
    goto :error_handler
) else (
    echo SUCCESS: Concatenate Postpanel Unsubs completed successfully
)
echo.

:: Step 6: Mathews Unsubs Upload to DBMS
echo [%time%] Step 6/6: Running Mathews Unsubs Upload to DBMS...
echo ----------------------------------------
python "mathews_unsubs_upload.py"
if !errorlevel! neq 0 (
    echo ERROR: Mathews Unsubs Upload to DBMS failed with error code !errorlevel!
    set "error_occurred=true"
    goto :error_handler
) else (
    echo SUCCESS: Mathews Unsubs Upload to DBMS completed successfully
)
echo.

:: Success completion
echo ========================================
echo    WORKFLOW COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo All 6 steps completed successfully at %date% %time%
echo.
echo Summary:
echo ✓ CMS Mathews Unsubs CSV Download
echo ✓ Mathews Postpanel Info PP Unsubs CSV Download
echo ✓ MI Conferences Postpanel Info PP Unsubs CSV Download
echo ✓ Concatenate CMS Unsubs
echo ✓ Concatenate Postpanel Unsubs
echo ✓ Mathews Unsubs Upload to DBMS
echo.
goto :end

:error_handler
echo.
echo ========================================
echo         WORKFLOW FAILED!
echo ========================================
echo.
echo An error occurred during the workflow execution.
echo Please check the error messages above and fix any issues.
echo.
echo Failed at: %date% %time%
echo.
echo You may need to:
echo - Check if all required files exist
echo - Verify network connectivity
echo - Ensure proper permissions
echo - Check Python script dependencies
echo.
pause
exit /b 1

:end
echo Press any key to exit...
pause >nul
exit /b 0
