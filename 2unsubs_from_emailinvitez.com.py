import os
import time
import sys
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By

# Import the rich progress module
from rich_progress import wait_for_download_with_progress, print_status, RICH_AVAILABLE

path_dir = r'H:\Master Bounces and Unsubs\Postpanel Unsubs\mw'

if not os.path.exists(path_dir):
    os.makedirs(path_dir)
os.chdir(path_dir)

# The wait_for_download_with_progress function is now imported from rich_progress module

# Selenium setup
options = webdriver.ChromeOptions()

# Suppress console logging messages
options.add_experimental_option("excludeSwitches", ["enable-logging"])

# Add logging preferences to capture browser logs
options.add_argument('log-level=3')  # Set log level to errors only

# Headless mode and other performance settings
options.add_argument("--headless")  # Run in headless mode
options.add_argument("--disable-gpu")
options.add_argument("--no-sandbox")
options.add_argument("--disable-dev-shm-usage")

# Disable extensions and other features that might cause errors
options.add_argument("--disable-extensions")
options.add_argument("--disable-notifications")

# Download preferences
prefs = {
    "download.default_directory": path_dir,
    "download.prompt_for_download": False,
    "download.directory_upgrade": True,
    "browser.download.show_plugins_in_list": False,
    "browser.download.folderList": 2,
    "browser.download.manager.showWhenStarting": False,
    "download.manager.showTaskbarProgress": False  # Disable taskbar progress updates
}
options.add_experimental_option('prefs', prefs)

# Print header
print_status("Starting EMAILINVITEZ.COM Unsubscribes Download Process", "header")

# Perform the automated download process
try:
    print_status("Initializing Chrome browser...", "info")
    driver = webdriver.Chrome(options=options)
    driver.maximize_window()
    driver.set_window_size(1920, 1080)

    print_status("Navigating to cPanel login...", "info")
    driver.get("https://103-211-218-158.cprapid.com:2087/")
    time.sleep(3)

    print_status("Logging in...", "info")
    driver.find_element(By.ID, "user").send_keys("root")
    driver.find_element(By.ID, "pass").send_keys("Magnus@12527")
    driver.find_element(By.XPATH, "//button[@id='login_submit']").click()
    time.sleep(3)

    print_status("Navigating to List Accounts...", "info")
    driver.find_element(By.XPATH, "//span[@class='cp-app__details-title'][normalize-space()='List Accounts']").click()
    time.sleep(3)

    print_status("Opening account details...", "info")
    driver.find_element(By.XPATH, "//input[@type='image']").click()
    time.sleep(5)

    print_status("Switching to phpMyAdmin...", "info")
    driver.switch_to.window(driver.window_handles[1])
    driver.find_element(By.XPATH, "//span[normalize-space()='phpMyAdmin']").click()
    time.sleep(5)

    print_status("Navigating to database...", "info")
    driver.switch_to.window(driver.window_handles[2])
    driver.find_element(By.XPATH, "//a[normalize-space()='emailinvitez_emailinvitez_db']").click()
    time.sleep(3)

    print_status("Searching for subscriber table...", "info")
    driver.find_element(By.XPATH, '//*[@id="filterText"]').send_keys('mw_list_subscriber')
    driver.find_element(By.XPATH, '//*[@id="row_tbl_116"]/th/a').click()
    time.sleep(10)

    print_status("Navigating to export...", "info")
    driver.find_element(By.XPATH, '//*[@id="topmenu"]/li[6]/a').click()
    time.sleep(6)

    print_status("Setting up CSV export...", "info")
    driver.find_element(By.XPATH, '//*[@id="plugins"]/option[2]').click()
    time.sleep(6)

    print_status("Initiating download...", "info")
    driver.find_element(By.XPATH, '//*[@id="buttonGo"]').click()

    # Wait for the download to complete with progress bar (using orange gradient)
    try:
        wait_for_download_with_progress(path_dir, timeout=60, color_scheme="orange")
        print_status("Download process completed successfully!", "success")
    except TimeoutError as e:
        print_status(f"Download timeout: {str(e)}", "error")

except Exception as e:
    print_status(f"Error during execution: {str(e)}", "error")

finally:
    # Always close the browser to clean up resources
    try:
        if 'driver' in locals():
            print_status("Closing browser...", "info")
            driver.quit()
    except Exception as close_error:
        print_status(f"Error closing browser: {str(close_error)}", "error")